{"Files": [{"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\mvc\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\mvc\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\mvc\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\mvc\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\mvc\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.MvcApp.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.MvcApp.props", "PackagePath": "build\\MvcApp.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.MvcApp.props", "PackagePath": "buildMultiTargeting\\MvcApp.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.MvcApp.props", "PackagePath": "buildTransitive\\MvcApp.props"}], "ElementsToRemove": []}