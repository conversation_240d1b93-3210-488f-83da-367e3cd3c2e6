<!DOCTYPE html>
<html>
<head>
    <title>Test MOSCI Search Functionality</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>MOSCI Search Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <ol>
            <li>Open the MOSCI page: <a href="http://localhost:5000/Transfers/Mosci" target="_blank">http://localhost:5000/Transfers/Mosci</a></li>
            <li>Open browser developer tools (F12) to see console logs</li>
            <li>Test the following scenarios:</li>
        </ol>
        
        <h3>Test Scenarios:</h3>
        <div class="test-section">
            <h4>Scenario 1: Valid Search - A123456</h4>
            <ul>
                <li>Select prefix: <strong>A</strong></li>
                <li>Enter Offender ID: <strong>123456</strong></li>
                <li>Click "Find Inmate" button</li>
                <li><strong>Expected:</strong> Should find 2 inmates (SMITH, JOHN and JOHNSON, ROBERT)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h4>Scenario 2: Valid Search - R345678</h4>
            <ul>
                <li>Select prefix: <strong>R</strong></li>
                <li>Enter Offender ID: <strong>345678</strong></li>
                <li>Click "Find Inmate" button</li>
                <li><strong>Expected:</strong> Should find 1 inmate (WILLIAMS, MICHAEL)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h4>Scenario 3: Valid Search - A456789</h4>
            <ul>
                <li>Select prefix: <strong>A</strong></li>
                <li>Enter Offender ID: <strong>456789</strong></li>
                <li>Click "Find Inmate" button</li>
                <li><strong>Expected:</strong> Should find 1 inmate (BROWN, DAVID with prefix A)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h4>Scenario 4: Invalid Search - A999999</h4>
            <ul>
                <li>Select prefix: <strong>A</strong></li>
                <li>Enter Offender ID: <strong>999999</strong></li>
                <li>Click "Find Inmate" button</li>
                <li><strong>Expected:</strong> Should show "No inmates found" message</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h4>Scenario 5: Empty Search</h4>
            <ul>
                <li>Leave Offender ID field empty</li>
                <li>Click "Find Inmate" button</li>
                <li><strong>Expected:</strong> Should show validation message "Please enter an Offender ID"</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h4>Scenario 6: Enter Key Test</h4>
            <ul>
                <li>Select prefix: <strong>A</strong></li>
                <li>Enter Offender ID: <strong>123456</strong></li>
                <li>Press <strong>Enter</strong> key in the Offender ID field</li>
                <li><strong>Expected:</strong> Should trigger search and find inmates</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>What to Check in Browser Console</h2>
        <ul>
            <li><span class="info">Form submission detected</span> - Should appear when form is submitted</li>
            <li><span class="info">Submit action: Search</span> - Should show "Search" as the action</li>
            <li><span class="info">Updated hidden fields</span> - Should show the search values being set</li>
            <li><span class="info">Search values being submitted</span> - Should show the final values sent to server</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Sample Data Available for Testing</h2>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr>
                <th>Prefix</th>
                <th>Offender ID</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Combined ID</th>
            </tr>
            <tr>
                <td>A</td>
                <td>123456</td>
                <td>SMITH</td>
                <td>JOHN</td>
                <td>A123456</td>
            </tr>
            <tr>
                <td>A</td>
                <td>123456</td>
                <td>JOHNSON</td>
                <td>ROBERT</td>
                <td>A123456</td>
            </tr>
            <tr>
                <td>R</td>
                <td>345678</td>
                <td>WILLIAMS</td>
                <td>MICHAEL</td>
                <td>R345678</td>
            </tr>
            <tr>
                <td>A</td>
                <td>456789</td>
                <td>BROWN</td>
                <td>DAVID</td>
                <td>A456789</td>
            </tr>
            <tr>
                <td>W</td>
                <td>456789</td>
                <td>BROWN</td>
                <td>DAVID</td>
                <td>W456789</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>Troubleshooting</h2>
        <p>If the search is not working:</p>
        <ul>
            <li>Check browser console for JavaScript errors</li>
            <li>Verify that the form submission logs appear</li>
            <li>Check that SearchPrefix and SearchOffenderId values are being set</li>
            <li>Ensure the application is running on http://localhost:5000</li>
        </ul>
    </div>
</body>
</html>
